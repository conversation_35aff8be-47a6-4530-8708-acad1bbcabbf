import { PrismaService } from '@/lib/orm/orm.service';
import { Injectable, UnauthorizedException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { User } from 'generated/prisma/client';
import * as bcrypt from 'bcrypt';
import { jwtTokens, jwtTokensPayload } from './dto/jwtTokents.dto';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class AuthService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly jwtService: JwtService,
    private readonly configServer: ConfigService,
  ) {}

  async login(email: string, password: string): Promise<jwtTokens> {
    const user: User | null = (await this.prisma.user.findUnique({
      where: { email },
      select: { id: true, password: true },
    })) as User;
    if (!user) {
      throw new UnauthorizedException('Wrong Email');
    }
    const rightPassword = await bcrypt.compare(password, user.password);
    if (!rightPassword) {
      throw new UnauthorizedException('Wrong Password');
    }

    return this.jwtTokens({ email: user.email, sub: user.id });
  }

  async register(
    email: string,
    password: string,
    name: string,
  ): Promise<jwtTokens> {
    const user: User | null = (await this.prisma.user.findUnique({
      where: { email },
    })) as User;
    if (user) {
      throw new UnauthorizedException('User already exists');
    }
    const hashedPassword = await bcrypt.hash(
      password,
      String(this.configServer.get('JWT_SECRET')),
    );
    const newUser = await this.prisma.user.create({
      data: {
        email,
        password: hashedPassword,
        name,
      },
    });
    return this.jwtTokens({ email: newUser.email, sub: newUser.id });
  }

  async refresh(refreshToken: jwtTokens['refresh_token']) {
    const payload = await this.verifyToken(refreshToken);

    const accessPayload = {
      username: payload.username,
      sub: payload.sub,
    };
    const accessToken = this.jwtService.sign(accessPayload, {
      secret: String(this.configServer.get('JWT_SECRET')),
      expiresIn: '30m',
    });

    return { access_token: accessToken };
  }

  async verifyToken(token: string): Promise<any> {
    const payload = await this.jwtService.verifyAsync(token, {
      secret: String(this.configServer.get('JWT_SECRET')),
    });
    if (!payload) {
      throw new UnauthorizedException('Invalid or expired token');
    }
    return payload;
  }

  private jwtTokens(payload: jwtTokensPayload): jwtTokens {
    return {
      access_token: this.jwtService.sign(payload, {
        expiresIn: '30m',
        secret: String(this.configServer.get('JWT_SECRET')),
      }),
      refresh_token: this.jwtService.sign(payload, {
        expiresIn: '7d',
        secret: String(this.configServer.get('JWT_SECRET')),
      }),
    };
  }
}
