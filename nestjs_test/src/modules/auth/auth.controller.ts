import {
  Controller,
  Body,
  HttpCode,
  HttpStatus,
  Get,
  Post,
} from '@nestjs/common';
import { AuthService } from './auth.service';
import { LoginDto, RegisterDto, jwtTokens } from './dto/jwtTokents.dto';

@Controller('auth')
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @Get('login')
  @HttpCode(HttpStatus.OK)
  async login(@Body() loginDto: LoginDto): Promise<jwtTokens> {
    const { email, password } = loginDto;
    return this.authService.login(email, password);
  }

  @Post('register')
  @HttpCode(HttpStatus.CREATED)
  async register(@Body() registerDto: RegisterDto): Promise<jwtTokens> {
    const { email, password, name } = registerDto;
    return this.authService.register(email, password, name);
  }
}
